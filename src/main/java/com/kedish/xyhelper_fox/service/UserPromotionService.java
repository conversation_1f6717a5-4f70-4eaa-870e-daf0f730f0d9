package com.kedish.xyhelper_fox.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.kedish.xyhelper_fox.exception.FoxException;
import com.kedish.xyhelper_fox.model.req.PageQueryReq;
import com.kedish.xyhelper_fox.model.req.WithdrawReq;
import com.kedish.xyhelper_fox.repo.mapper.UserPromotionMapper;
import com.kedish.xyhelper_fox.repo.mapper.WithdrawRecordMapper;
import com.kedish.xyhelper_fox.repo.model.UserPromotion;
import com.kedish.xyhelper_fox.repo.model.WithdrawRecord;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Service
@Slf4j
public class UserPromotionService {

    @Resource
    private UserPromotionMapper userPromotionMapper;

    @Resource
    private WithdrawRecordMapper withdrawRecordMapper;

    public void addPromotionAmount(String userToken, BigDecimal amount) {

        UserPromotion userPromotion = userPromotionMapper
                .selectOne(new QueryWrapper<UserPromotion>().eq("user_token", userToken));

        if (userPromotion == null) {
            userPromotion = new UserPromotion();
            userPromotion.setUserToken(userToken);
            userPromotion.setPromotionAmount(amount);
            userPromotion.setPromotionOrderNum(1);
            userPromotion.setWaitWithdrawAmount(amount);
            userPromotion.setWithdrawAmount(BigDecimal.ZERO);
            userPromotion.setCreatedAt(LocalDateTime.now());
            userPromotion.setUpdatedAt(LocalDateTime.now());
            userPromotionMapper.insert(userPromotion);
        } else {
            userPromotion.setPromotionAmount(userPromotion.getPromotionAmount().add(amount));
            userPromotion.setPromotionOrderNum(userPromotion.getPromotionOrderNum() + 1);
            userPromotion.setWaitWithdrawAmount(userPromotion.getWaitWithdrawAmount().add(amount));
            userPromotion.setUpdatedAt(LocalDateTime.now());
            userPromotionMapper.updateById(userPromotion);

        }
    }

    /**
     * 查询用户推广信息
     * @param userToken 用户标识
     * @return 用户推广信息，如果不存在则返回null
     */
    public UserPromotion getPromotionInfo(String userToken) {
        log.info("查询用户推广信息，userToken: {}", userToken);

        UserPromotion userPromotion = userPromotionMapper
                .selectOne(new QueryWrapper<UserPromotion>().eq("user_token", userToken));

        if (userPromotion == null) {
            log.info("用户推广信息不存在，userToken: {}", userToken);
        } else {
            log.info("查询到用户推广信息，userToken: {}, 推广金额: {}, 推广订单数: {}, 待提现金额: {}, 已提现金额: {}",
                    userToken, userPromotion.getPromotionAmount(), userPromotion.getPromotionOrderNum(),
                    userPromotion.getWaitWithdrawAmount(), userPromotion.getWithdrawAmount());
        }

        return userPromotion;
    }

    /**
     * 分页查询用户推广信息
     * @param req 分页查询请求
     * @return 分页结果
     */
    public Page<UserPromotion> page(PageQueryReq req) {
        log.info("分页查询用户推广信息，pageNum: {}, pageSize: {}, query: {}",
                req.getPageNum(), req.getPageSize(), req.getQuery());

        Page<UserPromotion> page = new Page<>(req.getPageNum(), req.getPageSize());

        // 设置排序
        if (StringUtils.hasLength(req.getSortField()) && StringUtils.hasLength(req.getSortOrder())) {
            OrderItem orderItem = new OrderItem();
            orderItem.setColumn(req.getSortField());
            orderItem.setAsc("asc".equalsIgnoreCase(req.getSortOrder()));
            page.addOrder(orderItem);
        } else {
            // 默认按更新时间倒序排列
            page.addOrder(OrderItem.desc("updated_at"));
        }

        QueryWrapper<UserPromotion> queryWrapper = new QueryWrapper<>();

        // 如果有查询条件，支持按用户标识模糊查询
        if (StringUtils.hasLength(req.getQuery())) {
            queryWrapper.like("user_token", req.getQuery());
        }

        return userPromotionMapper.selectPage(page, queryWrapper);
    }

    /**
     * 用户申请提现
     * @param userToken 用户标识
     * @param withdrawReq 提现申请请求
     * @return 提现记录ID
     */
    public Long applyWithdraw(String userToken, WithdrawReq withdrawReq) {
        log.info("用户申请提现，userToken: {}, amount: {}", userToken, withdrawReq.getAmount());

        // 参数校验
        if (withdrawReq.getAmount() == null || withdrawReq.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new FoxException("提现金额必须大于0");
        }

        // 查询用户推广信息
        UserPromotion userPromotion = userPromotionMapper
                .selectOne(new QueryWrapper<UserPromotion>().eq("user_token", userToken));

        if (userPromotion == null) {
            throw new FoxException("用户暂无推广信息，无法申请提现");
        }

        // 检查待提现金额是否足够
        if (userPromotion.getWaitWithdrawAmount().compareTo(withdrawReq.getAmount()) < 0) {
            throw new FoxException("待提现金额不足，当前可提现金额：" + userPromotion.getWaitWithdrawAmount());
        }

        // 创建提现记录
        WithdrawRecord withdrawRecord = new WithdrawRecord();
        withdrawRecord.setUserToken(userToken);
        withdrawRecord.setAmount(withdrawReq.getAmount());
        withdrawRecord.setStatus(0); // 0-待处理
        withdrawRecord.setRemark(withdrawReq.getRemark());
        withdrawRecord.setQrCodeImage(withdrawReq.getQrCodeImageUrl()); // 直接使用传入的图片URL
        withdrawRecord.setWithdrawTime(LocalDateTime.now());
        withdrawRecord.setCreatedAt(LocalDateTime.now());
        withdrawRecord.setUpdatedAt(LocalDateTime.now());

        // 保存提现记录
        withdrawRecordMapper.insert(withdrawRecord);

        // 更新用户推广信息：减少待提现金额
        userPromotion.setWaitWithdrawAmount(userPromotion.getWaitWithdrawAmount().subtract(withdrawReq.getAmount()));
        userPromotion.setUpdatedAt(LocalDateTime.now());
        userPromotionMapper.updateById(userPromotion);

        log.info("提现申请创建成功，withdrawRecordId: {}, userToken: {}, amount: {}",
                withdrawRecord.getId(), userToken, withdrawReq.getAmount());

        return withdrawRecord.getId();
    }



    /**
     * 查询用户提现记录
     * @param userToken 用户标识
     * @param req 分页查询请求
     * @return 分页结果
     */
    public Page<WithdrawRecord> pageWithdrawRecords(String userToken, PageQueryReq req) {
        log.info("查询用户提现记录，userToken: {}, pageNum: {}, pageSize: {}",
                userToken, req.getPageNum(), req.getPageSize());

        Page<WithdrawRecord> page = new Page<>(req.getPageNum(), req.getPageSize());

        // 设置排序，默认按创建时间倒序
        if (StringUtils.hasLength(req.getSortField()) && StringUtils.hasLength(req.getSortOrder())) {
            OrderItem orderItem = new OrderItem();
            orderItem.setColumn(req.getSortField());
            orderItem.setAsc("asc".equalsIgnoreCase(req.getSortOrder()));
            page.addOrder(orderItem);
        } else {
            page.addOrder(OrderItem.desc("created_at"));
        }

        QueryWrapper<WithdrawRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_token", userToken);

        return withdrawRecordMapper.selectPage(page, queryWrapper);
    }

    /**
     * 管理员分页查询所有提现记录
     * @param req 分页查询请求
     * @return 分页结果
     */
    public Page<WithdrawRecord> pageAllWithdrawRecords(PageQueryReq req) {
        log.info("管理员查询所有提现记录，pageNum: {}, pageSize: {}, query: {}",
                req.getPageNum(), req.getPageSize(), req.getQuery());

        Page<WithdrawRecord> page = new Page<>(req.getPageNum(), req.getPageSize());

        // 设置排序，默认按创建时间倒序
        if (StringUtils.hasLength(req.getSortField()) && StringUtils.hasLength(req.getSortOrder())) {
            OrderItem orderItem = new OrderItem();
            orderItem.setColumn(req.getSortField());
            orderItem.setAsc("asc".equalsIgnoreCase(req.getSortOrder()));
            page.addOrder(orderItem);
        } else {
            page.addOrder(OrderItem.desc("created_at"));
        }

        QueryWrapper<WithdrawRecord> queryWrapper = new QueryWrapper<>();

        // 如果有查询条件，支持按用户标识模糊查询
        if (StringUtils.hasLength(req.getQuery())) {
            queryWrapper.like("user_token", req.getQuery());
        }

        return withdrawRecordMapper.selectPage(page, queryWrapper);
    }

    /**
     * 管理员处理提现申请
     * @param withdrawRecordId 提现记录ID
     * @param status 处理状态：1-成功，2-失败
     * @param remark 处理备注
     * @return 是否处理成功
     */
    public boolean processWithdraw(Long withdrawRecordId, Integer status, String remark) {
        log.info("管理员处理提现申请，withdrawRecordId: {}, status: {}, remark: {}",
                withdrawRecordId, status, remark);

        // 参数校验
        if (status == null || (status != 1 && status != 2)) {
            throw new FoxException("处理状态无效，必须为1(成功)或2(失败)");
        }

        // 查询提现记录
        WithdrawRecord withdrawRecord = withdrawRecordMapper.selectById(withdrawRecordId);
        if (withdrawRecord == null) {
            throw new FoxException("提现记录不存在");
        }

        // 检查记录状态
        if (withdrawRecord.getStatus() != 0) {
            throw new FoxException("该提现申请已被处理，当前状态：" + getStatusText(withdrawRecord.getStatus()));
        }

        // 如果处理结果是失败，需要将金额退回到用户的待提现金额
        if (status == 2) {
            UserPromotion userPromotion = userPromotionMapper
                    .selectOne(new QueryWrapper<UserPromotion>().eq("user_token", withdrawRecord.getUserToken()));

            if (userPromotion != null) {
                // 将提现金额退回到待提现金额
                userPromotion.setWaitWithdrawAmount(
                    userPromotion.getWaitWithdrawAmount().add(withdrawRecord.getAmount())
                );
                userPromotion.setUpdatedAt(LocalDateTime.now());
                userPromotionMapper.updateById(userPromotion);

                log.info("提现失败，金额已退回，userToken: {}, amount: {}",
                        withdrawRecord.getUserToken(), withdrawRecord.getAmount());
            }
        } else if (status == 1) {
            // 处理成功，更新用户推广信息中的已提现金额
            UserPromotion userPromotion = userPromotionMapper
                    .selectOne(new QueryWrapper<UserPromotion>().eq("user_token", withdrawRecord.getUserToken()));

            if (userPromotion != null) {
                userPromotion.setWithdrawAmount(
                    userPromotion.getWithdrawAmount().add(withdrawRecord.getAmount())
                );
                userPromotion.setUpdatedAt(LocalDateTime.now());
                userPromotionMapper.updateById(userPromotion);

                log.info("提现成功，已更新已提现金额，userToken: {}, amount: {}",
                        withdrawRecord.getUserToken(), withdrawRecord.getAmount());
            }
        }

        // 更新提现记录状态
        withdrawRecord.setStatus(status);
        if (remark != null && !remark.trim().isEmpty()) {
            // 如果原来有备注，追加新备注；否则直接设置
            String originalRemark = withdrawRecord.getRemark();
            if (originalRemark != null && !originalRemark.trim().isEmpty()) {
                withdrawRecord.setRemark(originalRemark + " | 处理备注：" + remark.trim());
            } else {
                withdrawRecord.setRemark("处理备注：" + remark.trim());
            }
        }
        withdrawRecord.setUpdatedAt(LocalDateTime.now());

        int updateResult = withdrawRecordMapper.updateById(withdrawRecord);

        log.info("提现申请处理完成，withdrawRecordId: {}, status: {}, result: {}",
                withdrawRecordId, status, updateResult > 0 ? "成功" : "失败");

        return updateResult > 0;
    }

    /**
     * 获取状态文本描述
     * @param status 状态码
     * @return 状态描述
     */
    private String getStatusText(Integer status) {
        switch (status) {
            case 0: return "待处理";
            case 1: return "成功";
            case 2: return "失败";
            default: return "未知状态";
        }
    }
}
