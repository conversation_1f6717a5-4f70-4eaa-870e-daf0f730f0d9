package com.kedish.xyhelper_fox.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kedish.xyhelper_fox.model.req.PageQueryReq;
import com.kedish.xyhelper_fox.model.req.ProcessWithdrawReq;
import com.kedish.xyhelper_fox.model.req.WithdrawReq;
import com.kedish.xyhelper_fox.model.resp.FoxPageResult;
import com.kedish.xyhelper_fox.model.resp.FoxResult;
import com.kedish.xyhelper_fox.repo.model.ChatgptUser;
import com.kedish.xyhelper_fox.repo.model.UserPromotion;
import com.kedish.xyhelper_fox.repo.model.WithdrawRecord;
import com.kedish.xyhelper_fox.security.UserContext;
import com.kedish.xyhelper_fox.service.UserPromotionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 用户推广控制器
 * 提供用户推广相关的API接口
 */
@RestController
@RequestMapping("/api/userPromotion")
@Slf4j
public class UserPromotionController extends BaseController {

    @Resource
    private UserPromotionService userPromotionService;

    /**
     * 查询当前用户的推广信息
     * @return 推广信息
     */
    @GetMapping("/myInfo")
    public FoxResult getMyPromotionInfo() {
        ChatgptUser currentUser = UserContext.getUser();
        String userToken = currentUser.getUserToken();
        
        log.info("查询用户推广信息，userToken: {}", userToken);
        
        UserPromotion promotionInfo = userPromotionService.getPromotionInfo(userToken);
        
        if (promotionInfo == null) {
            log.info("用户暂无推广信息，userToken: {}", userToken);
            // 返回默认的空推广信息
            UserPromotion emptyPromotion = new UserPromotion();
            emptyPromotion.setUserToken(userToken);
            emptyPromotion.setPromotionAmount(java.math.BigDecimal.ZERO);
            emptyPromotion.setPromotionOrderNum(0);
            emptyPromotion.setWaitWithdrawAmount(java.math.BigDecimal.ZERO);
            emptyPromotion.setWithdrawAmount(java.math.BigDecimal.ZERO);
            return FoxResult.ok(emptyPromotion);
        }
        
        return FoxResult.ok(promotionInfo);
    }

    /**
     * 管理员查询指定用户的推广信息
     * @param userToken 用户标识
     * @return 推广信息
     */
    @GetMapping("/info")
    public FoxResult getPromotionInfo(@RequestParam String userToken) {
        log.info("管理员查询用户推广信息，userToken: {}", userToken);
        
        // 检查管理员权限
        checkIsAdmin();
        
        UserPromotion promotionInfo = userPromotionService.getPromotionInfo(userToken);
        
        if (promotionInfo == null) {
            log.info("用户暂无推广信息，userToken: {}", userToken);
            return FoxResult.fail("用户暂无推广信息");
        }
        
        return FoxResult.ok(promotionInfo);
    }

    /**
     * 分页查询用户推广信息（管理员接口）
     * @param req 分页查询请求
     * @return 分页结果
     */
    @PostMapping("/page")
    public FoxPageResult pagePromotionInfo(@RequestBody PageQueryReq req) {
        log.info("分页查询用户推广信息，req: {}", JSON.toJSONString(req));

        // 检查管理员权限
        checkIsAdmin();

        Page<UserPromotion> page = userPromotionService.page(req);
        return FoxPageResult.fromPage(page);
    }

    /**
     * 用户申请提现
     * @param withdrawReq 提现申请请求
     * @return 提现记录ID
     */
    @PostMapping("/withdraw/apply")
    public FoxResult applyWithdraw(@RequestBody WithdrawReq withdrawReq) {
        ChatgptUser currentUser = UserContext.getUser();
        String userToken = currentUser.getUserToken();

        log.info("用户申请提现，userToken: {}, amount: {}, remark: {}, qrCodeImageUrl: {}",
                userToken, withdrawReq.getAmount(), withdrawReq.getRemark(), withdrawReq.getQrCodeImageUrl());

        Long withdrawRecordId = userPromotionService.applyWithdraw(userToken, withdrawReq);

        return FoxResult.ok(withdrawRecordId);
    }

    /**
     * 查询当前用户的提现记录
     * @param req 分页查询请求
     * @return 分页结果
     */
    @PostMapping("/withdraw/myRecords")
    public FoxPageResult getMyWithdrawRecords(@RequestBody PageQueryReq req) {
        ChatgptUser currentUser = UserContext.getUser();
        String userToken = currentUser.getUserToken();

        log.info("查询用户提现记录，userToken: {}, pageNum: {}, pageSize: {}",
                userToken, req.getPageNum(), req.getPageSize());

        Page<WithdrawRecord> page = userPromotionService.pageWithdrawRecords(userToken, req);
        return FoxPageResult.fromPage(page);
    }

    /**
     * 管理员查询所有提现记录
     * @param req 分页查询请求
     * @return 分页结果
     */
    @PostMapping("/withdraw/allRecords")
    public FoxPageResult getAllWithdrawRecords(@RequestBody PageQueryReq req) {
        log.info("管理员查询所有提现记录，req: {}", JSON.toJSONString(req));

        // 检查管理员权限
        checkIsAdmin();

        Page<WithdrawRecord> page = userPromotionService.pageAllWithdrawRecords(req);
        return FoxPageResult.fromPage(page);
    }

    /**
     * 管理员处理提现申请
     * @param processReq 处理请求
     * @return 处理结果
     */
    @PostMapping("/withdraw/process")
    public FoxResult processWithdraw(@RequestBody ProcessWithdrawReq processReq) {
        log.info("管理员处理提现申请，req: {}", JSON.toJSONString(processReq));

        // 检查管理员权限
        checkIsAdmin();

        // 参数校验
        if (processReq.getWithdrawRecordId() == null) {
            return FoxResult.fail("提现记录ID不能为空");
        }

        if (processReq.getStatus() == null) {
            return FoxResult.fail("处理状态不能为空");
        }

        if (processReq.getStatus() != 1 && processReq.getStatus() != 2) {
            return FoxResult.fail("处理状态无效，必须为1(成功)或2(失败)");
        }

        boolean result = userPromotionService.processWithdraw(
                processReq.getWithdrawRecordId(),
                processReq.getStatus(),
                processReq.getRemark()
        );

        if (result) {
            String statusText = processReq.getStatus() == 1 ? "成功" : "失败";
            return FoxResult.ok("提现申请处理" + statusText);
        } else {
            return FoxResult.fail("提现申请处理失败");
        }
    }
}
